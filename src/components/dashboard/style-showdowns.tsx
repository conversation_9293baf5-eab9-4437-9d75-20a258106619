"use client";

import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { StyleShowdown } from "@/hooks/useTrendingData";
import { ZapIcon, TrendingUpIcon, PaletteIcon, ShirtIcon, SparklesIcon } from "lucide-react";

interface StyleShowdownsProps {
  styleShowdowns?: StyleShowdown[];
  loading?: boolean;
  error?: string;
}

export function StyleShowdowns({ styleShowdowns, loading, error }: StyleShowdownsProps) {
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-[200px]" />
          <Skeleton className="h-4 w-[300px]" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="p-4 border rounded-lg">
                <Skeleton className="h-5 w-[300px] mb-2" />
                <Skeleton className="h-4 w-[200px] mb-2" />
                <Skeleton className="h-4 w-[150px]" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-red-600">Error Loading Style Showdowns</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">{error}</p>
        </CardContent>
      </Card>
    );
  }

  if (!styleShowdowns || styleShowdowns.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>No Style Showdowns Available</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">No style comparison data available at this time.</p>
        </CardContent>
      </Card>
    );
  }

  const getCategoryIcon = (category: string) => {
    switch (category.toLowerCase()) {
      case 'color':
        return PaletteIcon;
      case 'material':
        return SparklesIcon;
      case 'style':
        return ShirtIcon;
      default:
        return ZapIcon;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category.toLowerCase()) {
      case 'color':
        return 'bg-purple-100 text-purple-800';
      case 'material':
        return 'bg-blue-100 text-blue-800';
      case 'style':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUpIcon className="h-5 w-5" />
          Style Showdowns
        </CardTitle>
        <CardDescription>
          Head-to-head style comparisons revealing fashion preferences and trends
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="max-h-96 overflow-y-auto space-y-4">
          {styleShowdowns.map((showdown, index) => {
            const CategoryIcon = getCategoryIcon(showdown.category);
            const categoryColor = getCategoryColor(showdown.category);

            return (
              <div key={index} className="p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                <div className="space-y-3">
                  {/* Category and Date */}
                  <div className="flex items-center justify-between">
                    <Badge className={categoryColor}>
                      <CategoryIcon className="h-3 w-3 mr-1" />
                      {showdown.category.charAt(0).toUpperCase() + showdown.category.slice(1)}
                    </Badge>
                    <span className="text-xs text-muted-foreground">
                      {formatDate(showdown.analysis_date)}
                    </span>
                  </div>

                  {/* Main Insight */}
                  <div>
                    <h3 className="font-semibold text-lg mb-1">{showdown.insight}</h3>
                    <p className="text-sm text-muted-foreground">{showdown.comparison}</p>
                  </div>

                  {/* Metrics Breakdown */}
                  <div className="flex items-center justify-between pt-2 border-t">
                    <div className="flex items-center gap-4">
                      <div className="text-center">
                        <div className="text-lg font-bold text-green-600">
                          {showdown.primary_value.toLocaleString()}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Winner
                        </div>
                      </div>
                      <div className="text-muted-foreground">vs</div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-gray-500">
                          {showdown.secondary_value.toLocaleString()}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Runner-up
                        </div>
                      </div>
                    </div>

                    <div className="text-right">
                      <div className="text-sm font-medium">{showdown.comparison_type}</div>
                      <div className="text-xs text-muted-foreground">{showdown.metrics}</div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}
