"use client";

import React from "react";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Separator } from "@/components/ui/separator"

// Import trending components
import { TrendingMetadata } from "@/components/dashboard/trending-metadata"
import { SearchTrends } from "@/components/dashboard/search-trends"
import { StyleShowdowns } from "@/components/dashboard/style-showdowns"
import { BrandPerformance } from "@/components/dashboard/brand-performance"
import { TrendingProducts } from "@/components/dashboard/trending-products"

// Import the hook for trending data
import { useTrendingData } from "@/hooks/useTrendingData";

export default function TrendingPageClient() {
  // Fetch trending data
  const {
    data: trendingData,
    loading: trendingLoading,
    error: trendingError,
    refetch: refetchTrending
  } = useTrendingData();

  return (
    <div className="@container/main flex flex-1 flex-col gap-4 p-4 md:gap-6 md:p-6">
      {/* Breadcrumb Navigation */}
      <Breadcrumb className="hidden font-medium md:flex">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/home">Dashboard</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Trending</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      <Separator className="my-2 hidden md:block" />

      {/* Page Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Trending Analytics</h1>
        <p className="text-muted-foreground">
          Discover trending searches, style insights, brand performance, and product analytics.
        </p>
      </div>

      {/* Trending Metadata Overview */}
      <TrendingMetadata
        metadata={trendingData?.metadata}
        loading={trendingLoading}
        error={trendingError || undefined}
      />

      {/* Search Trends */}
      <SearchTrends
        searchTrends={trendingData?.search_trends}
        loading={trendingLoading}
        error={trendingError || undefined}
      />

      {/* Style Showdowns */}
      <StyleShowdowns
        styleShowdowns={trendingData?.style_showdowns}
        loading={trendingLoading}
        error={trendingError || undefined}
      />

      {/* Brand Performance */}
      <BrandPerformance
        brandPerformance={trendingData?.brand_performance}
        loading={trendingLoading}
        error={trendingError || undefined}
      />

      {/* Trending Products */}
      <TrendingProducts
        trendingProducts={trendingData?.trending_products}
        loading={trendingLoading}
        error={trendingError || undefined}
      />
    </div>
  );
}
