import { useState, useEffect, useCallback } from 'react';
import { createClient } from '@/supabase/client/client';

// Types for trending data based on the JSON schema
export interface TrendingMetadata {
  generated_at: string;
  total_brands: number;
  analysis_date: string;
  lookback_days: number;
  total_products: number;
  total_searches: number;
  total_showdowns: number;
}

export interface SearchTrend {
  query: string;
  last_search: string;
  first_search: string;
  search_count: number;
  unique_users: number;
  days_searched: number;
  ios_percentage: number;
  web_percentage: number;
  recent_searches: number;
  unique_sessions: number;
  growth_percentage: number;
  operating_systems: string;
  previous_searches: number;
  searches_per_user: number;
  android_percentage: number;
  avg_searches_per_day: number;
  example_original_query: string;
}

export interface StyleShowdown {
  insight: string;
  metrics: string;
  category: string;
  comparison: string;
  analysis_date: string;
  primary_value: number;
  comparison_type: string;
  secondary_value: number;
}

export interface BrandPerformanceData {
  brand: string;
  last_seen: string;
  first_seen: string;
  days_active: number;
  unique_users: number;
  ios_percentage: number;
  trend_velocity: number;
  search_mentions: number;
  unique_sessions: number;
  product_searches: number;
  total_engagement: number;
  recent_engagement: number;
  safari_percentage: number;
  search_percentage: number;
  engagement_per_day: number;
  total_interactions: number;
  engagement_per_user: number;
  previous_engagement: number;
  product_search_percentage: number;
}

export interface TrendingProduct {
  brand: string;
  avg_price: number;
  sample_url: string;
  product_key: string;
  product_name: string;
  search_count: number;
  unique_users: number;
  days_searched: number;
  last_searched: string;
  first_searched: string;
  ios_percentage: number;
  trending_score: number;
  recent_searches: number;
  unique_sessions: number;
  searches_per_day: number;
  previous_searches: number;
  safari_percentage: number;
  searches_per_user: number | null;
  image_search_percentage: number;
}

export interface TrendingData {
  metadata: TrendingMetadata;
  search_trends: SearchTrend[];
  style_showdowns: StyleShowdown[];
  brand_performance: BrandPerformanceData[];
  trending_products: TrendingProduct[];
}

export interface UseTrendingDataOptions {
  autoFetch?: boolean;
}

export interface UseTrendingDataReturn {
  data: TrendingData | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export function useTrendingData(options: UseTrendingDataOptions = {}): UseTrendingDataReturn {
  const { autoFetch = true } = options;

  const [data, setData] = useState<TrendingData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchTrendingData = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const supabase = createClient();
      
      // Fetch the most recent analytics insights
      const { data: insights, error: fetchError } = await supabase
        .from('analytics_insights')
        .select('*')
        .eq('insight_type', 'daily_trending')
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (fetchError) {
        throw new Error(`Failed to fetch trending data: ${fetchError.message}`);
      }

      if (!insights || !insights.data) {
        throw new Error('No trending data available');
      }

      // Parse the JSON data
      const trendingData = insights.data as TrendingData;
      setData(trendingData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error fetching trending data:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    if (autoFetch) {
      fetchTrendingData();
    }
  }, [autoFetch, fetchTrendingData]);

  return {
    data,
    loading,
    error,
    refetch: fetchTrendingData,
  };
}
