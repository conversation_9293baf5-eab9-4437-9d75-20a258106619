"use client";

import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { SearchTrend } from "@/hooks/useTrendingData";
import { TrendingUpIcon, TrendingDownIcon, SearchIcon, UsersIcon, CalendarIcon } from "lucide-react";

interface SearchTrendsProps {
  searchTrends?: SearchTrend[];
  loading?: boolean;
  error?: string;
}

export function SearchTrends({ searchTrends, loading, error }: SearchTrendsProps) {
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-[200px]" />
          <Skeleton className="h-4 w-[300px]" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 10 }).map((_, i) => (
              <div key={i} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-5 w-[200px]" />
                  <Skeleton className="h-4 w-[150px]" />
                </div>
                <div className="space-y-2">
                  <Skeleton className="h-6 w-[80px]" />
                  <Skeleton className="h-4 w-[60px]" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-red-600">Error Loading Search Trends</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">{error}</p>
        </CardContent>
      </Card>
    );
  }

  if (!searchTrends || searchTrends.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>No Search Trends Available</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">No search trend data available at this time.</p>
        </CardContent>
      </Card>
    );
  }

  const formatGrowth = (growth: number) => {
    const isPositive = growth >= 0;
    return {
      value: `${isPositive ? '+' : ''}${growth.toFixed(1)}%`,
      isPositive,
      icon: isPositive ? TrendingUpIcon : TrendingDownIcon,
      color: isPositive ? 'text-green-600' : 'text-red-600',
      bgColor: isPositive ? 'bg-green-50' : 'bg-red-50',
    };
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
    });
  };

  // Show top 20 search trends
  const topTrends = searchTrends.slice(0, 20);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <SearchIcon className="h-5 w-5" />
          Search Trends
        </CardTitle>
        <CardDescription>
          Top trending search queries with growth metrics and user engagement
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="max-h-96 overflow-y-auto space-y-4">
          {topTrends.map((trend, index) => {
            const growth = formatGrowth(trend.growth_percentage);

            return (
              <div key={index} className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                <div className="space-y-2 flex-1">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-muted-foreground">#{index + 1}</span>
                    <h3 className="font-semibold capitalize">{trend.example_original_query}</h3>
                  </div>

                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <SearchIcon className="h-3 w-3" />
                      {trend.search_count.toLocaleString()} searches
                    </div>
                    <div className="flex items-center gap-1">
                      <UsersIcon className="h-3 w-3" />
                      {trend.unique_users.toLocaleString()} users
                    </div>
                    <div className="flex items-center gap-1">
                      <CalendarIcon className="h-3 w-3" />
                      {trend.days_searched} days
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="text-xs">
                      {trend.operating_systems}
                    </Badge>
                    <span className="text-xs text-muted-foreground">
                      {trend.avg_searches_per_day.toFixed(1)} searches/day
                    </span>
                  </div>
                </div>

                <div className="text-right space-y-2">
                  <div className={`flex items-center gap-1 ${growth.color}`}>
                    <growth.icon className="h-4 w-4" />
                    <span className="font-semibold">{growth.value}</span>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {formatDate(trend.first_search)} - {formatDate(trend.last_search)}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}
