const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')

// Load environment variables
require('dotenv').config()

async function setupDatabaseFunctions() {
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY
  )

  try {
    console.log('Setting up database functions...')
    
    // Read the SQL file
    const sqlPath = path.join(__dirname, 'supabase/migrations/daily_metrics_function.sql')
    const sql = fs.readFileSync(sqlPath, 'utf8')
    
    // Split by statements and execute each one
    const statements = sql.split(';').filter(stmt => stmt.trim())
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i].trim()
      if (statement) {
        console.log(`Executing statement ${i + 1}/${statements.length}...`)
        const { error } = await supabase.rpc('query', { query_text: statement + ';' })
        
        if (error) {
          // Try direct SQL execution if R<PERSON> fails
          console.log('R<PERSON> failed, trying direct execution...')
          const { error: directError } = await supabase.from('').select().textSearch().sql(statement + ';')
          
          if (directError) {
            console.error(`Error executing statement ${i + 1}:`, directError)
            // Don't exit on error, continue with other statements
          }
        }
      }
    }
    
    console.log('Database functions setup completed!')
    
  } catch (error) {
    console.error('Error setting up database functions:', error)
    process.exit(1)
  }
}

setupDatabaseFunctions()