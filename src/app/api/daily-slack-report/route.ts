import { NextRequest, NextResponse } from 'next/server'
import { IncomingWebhook } from '@slack/webhook'
import { 
  generateDailyReport, 
  type DailyReport, 
  type PlatformMetrics, 
  type MetricsComparison 
} from '@/lib/daily-metrics-queries'

// Initialize Slack client
const slack = new IncomingWebhook(process.env.SLACK_WEBHOOK_URL!)

// Security check for cron jobs
function verifyCronSecret(request: NextRequest): boolean {
  const authHeader = request.headers.get('authorization')
  const expectedAuth = `Bearer ${process.env.CRON_SECRET}`
  return authHeader === expectedAuth
}

// Format currency values
function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-US', { 
    style: 'currency', 
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}

// Format percentage change
function formatPercentChange(percent: number): string {
  const sign = percent >= 0 ? '+' : ''
  return `${sign}${percent.toFixed(1)}%`
}

// Create Slack message with Block Kit formatting
function formatSlackMessage(report: DailyReport) {
  const formatDate = (dateStr: string) => {
    return new Date(dateStr + 'T00:00:00').toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const createPlatformSection = (platformName: string, metrics: PlatformMetrics | null, comparison: MetricsComparison | null) => {
    if (!metrics) {
      return {
        type: "section",
        text: {
          type: "mrkdwn",
          text: `*${platformName}*\nNo data available for yesterday`
        }
      }
    }

    const gmvChange = comparison ? formatPercentChange(comparison.gmv_change_percent) : 'N/A'
    const aovChange = comparison ? formatPercentChange(comparison.aov_change_percent) : 'N/A'
    const revenueChange = comparison ? formatPercentChange(comparison.revenue_change_percent) : 'N/A'
    const transactionChange = comparison && comparison.previous_transactions > 0 
      ? formatPercentChange(((comparison.current_transactions - comparison.previous_transactions) / comparison.previous_transactions) * 100)
      : 'N/A'

    return {
      type: "section",
      fields: [
        {
          type: "mrkdwn",
          text: `*${platformName} Transactions:*\n${metrics.total_transactions.toLocaleString()} (${transactionChange})`
        },
        {
          type: "mrkdwn",
          text: `*${platformName} GMV:*\n${formatCurrency(metrics.total_gmv)} (${gmvChange})`
        },
        {
          type: "mrkdwn",
          text: `*${platformName} AOV:*\n${formatCurrency(metrics.average_order_value)} (${aovChange})`
        },
        {
          type: "mrkdwn",
          text: `*${platformName} Revenue:*\n${formatCurrency(metrics.total_revenue)} (${revenueChange})`
        }
      ]
    }
  }

  // Calculate combined totals
  const combinedGMV = (report.strackr?.total_gmv || 0) + (report.shopmy?.total_gmv || 0)
  const combinedRevenue = (report.strackr?.total_revenue || 0) + (report.shopmy?.total_revenue || 0)
  const combinedTransactions = (report.strackr?.total_transactions || 0) + (report.shopmy?.total_transactions || 0)

  return {
    text: `Daily Transaction Report - ${formatDate(report.date)}`,
    blocks: [
      {
        type: "header",
        text: {
          type: "plain_text",
          text: `📊 Daily Report - ${formatDate(report.date)}`
        }
      },
      {
        type: "section",
        text: {
          type: "mrkdwn",
          text: `*Combined Totals*\n• Transactions: ${combinedTransactions.toLocaleString()}\n• GMV: ${formatCurrency(combinedGMV)}\n• Revenue: ${formatCurrency(combinedRevenue)}`
        }
      },
      {
        type: "divider"
      },
      createPlatformSection("Strackr", report.strackr, report.comparison.strackr),
      {
        type: "divider"  
      },
      createPlatformSection("ShopMy", report.shopmy, report.comparison.shopmy),
      {
        type: "context",
        elements: [
          {
            type: "mrkdwn",
            text: "📈 Percentages show change vs. previous day | Generated by Phia Dashboard"
          }
        ]
      }
    ]
  }
}

// Send notification to Slack with retry logic
async function sendSlackNotification(report: DailyReport, maxRetries = 3): Promise<void> {
  const message = formatSlackMessage(report)
  
  for (let i = 0; i < maxRetries; i++) {
    try {
      await slack.send(message)
      console.log('Slack notification sent successfully')
      return
    } catch (error) {
      console.error(`Slack notification attempt ${i + 1} failed:`, error)
      if (i === maxRetries - 1) throw error
      // Exponential backoff: wait 1s, 2s, 4s
      await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, i)))
    }
  }
}

// Send error notification to Slack
async function notifyError(error: Error): Promise<void> {
  try {
    await slack.send({
      text: '🚨 Daily Report Failed',
      blocks: [{
        type: "section",
        text: {
          type: "mrkdwn", 
          text: `*Error:*\n\`\`\`${error.message}\`\`\`\n\n*Time:* ${new Date().toISOString()}`
        }
      }]
    })
  } catch (slackError) {
    // Log to Vercel's monitoring if Slack fails
    console.error('Critical: Failed to send error notification', slackError)
  }
}

// Main GET handler
export async function GET(request: NextRequest) {
  // Verify this is a legitimate cron job request
  if (!verifyCronSecret(request)) {
    console.error('Unauthorized cron job request')
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  try {
    console.log('Starting daily report generation...')
    
    // Generate report with timeout protection
    const report = await Promise.race([
      generateDailyReport(),
      new Promise<never>((_, reject) => 
        setTimeout(() => reject(new Error('Operation timeout after 50 seconds')), 50000)
      )
    ])

    console.log('Report generated, sending to Slack...')
    
    // Send to Slack
    await sendSlackNotification(report)

    console.log('Daily report completed successfully')
    return NextResponse.json({ 
      success: true, 
      message: 'Daily report sent successfully',
      date: report.date,
      platforms: {
        strackr: report.strackr ? 'success' : 'no data',
        shopmy: report.shopmy ? 'success' : 'no data'
      }
    })

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    console.error('Cron job failed:', errorMessage, error)
    
    // Send error notification (don't let this failure break the response)
    try {
      await notifyError(error instanceof Error ? error : new Error(errorMessage))
    } catch (notifyErr) {
      console.error('Failed to send error notification:', notifyErr)
    }
    
    return NextResponse.json({ 
      error: 'Internal server error', 
      message: errorMessage 
    }, { status: 500 })
  }
}