"use client";

import * as React from "react";
import { <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON><PERSON><PERSON>s, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts";
import {
  <PERSON>,
  Card<PERSON>ontent,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import { AppleAnalyticsData } from '@/hooks/useAppleAnalytics';
import { formatLargeNumber } from '@/lib/analytics-utils';

interface AppleAnalyticsChartsProps {
  analyticsData?: AppleAnalyticsData;
  loading?: boolean;
  error?: string;
}

export function AppleAnalyticsCharts({
  analyticsData,
  loading,
  error
}: AppleAnalyticsChartsProps) {

  // Prepare daily install/uninstall chart data (replacing DAU since that data is no longer available)
  const dailyInstallData = React.useMemo(() => {
    if (!analyticsData?.metrics.dailyTrends) return [];

    return analyticsData.metrics.dailyTrends.map(day => ({
      date: day.date,
      installs: day.installs,
      uninstalls: day.uninstalls,
      net: day.installs - day.uninstalls
    }));
  }, [analyticsData]);

  // Prepare version install performance data (replacing session analytics)
  const versionPerformanceData = React.useMemo(() => {
    if (!analyticsData?.metrics.versionBreakdown) return [];

    // Use the version breakdown data from our metrics
    return analyticsData.metrics.versionBreakdown
      .map(version => ({
        version: version.app_version,
        installs: version.installs,
        uninstalls: version.uninstalls,
        netInstalls: version.installs - version.uninstalls,
        installRate: version.installs > 0 ? ((version.installs - version.uninstalls) / version.installs * 100) : 0
      }))
      .sort((a, b) => b.installs - a.installs)
      .slice(0, 10); // Top 10 versions by installs
  }, [analyticsData]);

  // Calculate install/uninstall metrics for summary cards
  const totalMetrics = React.useMemo(() => {
    if (!analyticsData?.metrics) return null;

    const { metrics } = analyticsData;
    const avgDailyInstalls = metrics.dailyTrends.length > 0
      ? metrics.totalInstalls / metrics.dailyTrends.length
      : 0;
    const avgDailyUninstalls = metrics.dailyTrends.length > 0
      ? metrics.totalUninstalls / metrics.dailyTrends.length
      : 0;

    // Calculate retention rate (installs - uninstalls) / installs
    const retentionRate = metrics.totalInstalls > 0
      ? ((metrics.totalInstalls - metrics.totalUninstalls) / metrics.totalInstalls * 100)
      : 0;

    return {
      totalInstalls: metrics.totalInstalls,
      totalUninstalls: metrics.totalUninstalls,
      netInstalls: metrics.netInstalls,
      avgDailyInstalls: avgDailyInstalls.toFixed(0),
      avgDailyUninstalls: avgDailyUninstalls.toFixed(0),
      retentionRate: retentionRate.toFixed(1)
    };
  }, [analyticsData]);

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          {Array.from({ length: 2 }).map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <CardTitle>Loading...</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center">
                  <div className="text-sm text-muted-foreground">Loading chart...</div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="text-sm text-destructive">Error loading charts: {error}</div>
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <div className="space-y-6">
        <div className="text-sm text-muted-foreground">No chart data available</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Install/Uninstall Analytics Cards */}
      {totalMetrics && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Install/Uninstall Analytics</h3>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Total Installs
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatLargeNumber(totalMetrics.totalInstalls)}</div>
                <p className="text-xs text-muted-foreground mt-1">
                  All time period
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Net Installs
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatLargeNumber(totalMetrics.netInstalls)}</div>
                <p className="text-xs text-muted-foreground mt-1">
                  Installs - Uninstalls
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Avg Daily Installs
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{totalMetrics.avgDailyInstalls}</div>
                <p className="text-xs text-muted-foreground mt-1">
                  Per day average
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Retention Rate
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{totalMetrics.retentionRate}%</div>
                <p className="text-xs text-muted-foreground mt-1">
                  Install retention
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Daily Install/Uninstall Chart */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Daily Install/Uninstall Trends</h3>
        <Card>
          <CardContent className="pt-6">
            <ResponsiveContainer width="100%" height={400}>
              <BarChart data={dailyInstallData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                <XAxis
                  dataKey="date"
                  tickFormatter={(value) => {
                    const date = new Date(value + 'T12:00:00');
                    return date.toLocaleDateString("en-US", {
                      month: "short",
                      day: "numeric",
                    });
                  }}
                  className="text-muted-foreground"
                />
                <YAxis className="text-muted-foreground" />
                <Tooltip
                  content={({ active, payload, label }) => {
                    if (active && payload && payload.length) {
                      const data = payload[0].payload;
                      return (
                        <div className="bg-card border border-border rounded-lg p-3 shadow-lg">
                          <p className="font-medium text-card-foreground mb-2">
                            {new Date(label + 'T12:00:00').toLocaleDateString("en-US", {
                              weekday: "long",
                              month: "short",
                              day: "numeric",
                            })}
                          </p>
                          <div className="space-y-1 text-sm text-card-foreground">
                            <p><span className="font-medium">Installs:</span> {formatLargeNumber(data.installs)}</p>
                            <p><span className="font-medium">Uninstalls:</span> {formatLargeNumber(data.uninstalls)}</p>
                            <p><span className="font-medium">Net Installs:</span> {formatLargeNumber(data.net)}</p>
                          </div>
                        </div>
                      );
                    }
                    return null;
                  }}
                />
                <Bar
                  dataKey="installs"
                  fill="#10b981"
                  radius={[4, 4, 0, 0]}
                />
                <Bar
                  dataKey="uninstalls"
                  fill="#ef4444"
                  radius={[4, 4, 0, 0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Version Install Performance Chart */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">App Version Install Performance</h3>
        <Card>
          <CardContent className="pt-6">
            <ResponsiveContainer width="100%" height={400}>
              <BarChart data={versionPerformanceData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                <XAxis
                  dataKey="version"
                  angle={-45}
                  textAnchor="end"
                  height={80}
                  className="text-muted-foreground"
                />
                <YAxis className="text-muted-foreground" />
                <Tooltip
                  content={({ active, payload, label }) => {
                    if (active && payload && payload.length) {
                      const data = payload[0].payload;
                      return (
                        <div className="bg-card border border-border rounded-lg p-3 shadow-lg">
                          <p className="font-medium text-card-foreground mb-2">Version {label}</p>
                          <div className="space-y-1 text-sm text-card-foreground">
                            <p><span className="font-medium">Total Installs:</span> {formatLargeNumber(data.installs)}</p>
                            <p><span className="font-medium">Total Uninstalls:</span> {formatLargeNumber(data.uninstalls)}</p>
                            <p><span className="font-medium">Net Installs:</span> {formatLargeNumber(data.netInstalls)}</p>
                            <p><span className="font-medium">Install Rate:</span> {data.installRate.toFixed(1)}%</p>
                          </div>
                        </div>
                      );
                    }
                    return null;
                  }}
                />
                <Bar
                  dataKey="installs"
                  fill="#10b981"
                  radius={[4, 4, 0, 0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
