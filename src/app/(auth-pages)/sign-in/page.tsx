import { signInWithGoogleAction, skipSignInAction } from "@/app/actions";
import { FormMessage, Message } from "@/components/form-message";
import { SubmitButton } from "@/components/submit-button";
import { Icons } from "@/components/ui/icons";
import Image from "next/image";

export default async function SignInPage(props: { searchParams: Promise<Message> }) {
  const searchParams = await props.searchParams;

  return (
    <>
      <div className="absolute top-0 left-0 p-4 md:p-6 lg:p-8">
        <div className="flex items-center">
          <Image
            src="/logo.png"
            alt="Logo"
            width={60}
            height={60}
            className="filter invert brightness-0 dark:filter-none dark:invert-0"
          />
        </div>
      </div>

      <div className="flex-1 flex flex-col w-full justify-center items-center gap-6">
        <h1 className="text-4xl font-semibold text-white">Login</h1>
        <div className="w-full max-w-xs space-y-4">
          {/* Google Sign In */}
          <form action={signInWithGoogleAction}>
            <SubmitButton
              pendingText="Redirecting..."
              className="w-full bg-zinc-800 hover:bg-zinc-700 text-white border border-zinc-700 flex items-center justify-center gap-2"
            >
              <Icons.google className="h-5 w-5" />
              Continue with Google
            </SubmitButton>
          </form>

          {/* Skip Sign In for Local Testing */}
          <form action={skipSignInAction}>
            <SubmitButton
              pendingText="Skipping..."
              className="w-full bg-zinc-600 hover:bg-zinc-500 text-white border border-zinc-500 text-sm"
            >
              Skip Sign In (Local Testing)
            </SubmitButton>
          </form>
          
          <FormMessage message={searchParams} />
        </div>
      </div>
    </>
  );
}
