-- Function to get previous day metrics for both Strackr and ShopMy platforms
-- This function optimizes performance by doing aggregation at the database level

CREATE OR REPLACE FUNCTION get_previous_day_metrics(
  target_date DATE DEFAULT CURRENT_DATE - INTERVAL '1 day'
)
RETURNS TABLE (
  platform TEXT,
  total_transactions BIGINT,
  total_gmv DECIMAL(15,2),
  average_order_value DECIMAL(15,2),
  total_revenue DECIMAL(15,2)
) 
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    nt.platform::TEXT,
    COUNT(*)::BIGINT as total_transactions,
    COALESCE(SUM(
      COALESCE(nt.final_order_amount, nt.order_amount, 0)
    ), 0)::DECIMAL(15,2) as total_gmv,
    CASE 
      WHEN COUNT(*) FILTER (WHERE COALESCE(nt.final_order_amount, nt.order_amount, 0) > 0) > 0 THEN
        (COALESCE(SUM(
          CASE WHEN COALESCE(nt.final_order_amount, nt.order_amount, 0) > 0 
               THEN COALESCE(nt.final_order_amount, nt.order_amount, 0) 
               ELSE 0 END
        ), 0) / COUNT(*) FILTER (WHERE COALESCE(nt.final_order_amount, nt.order_amount, 0) > 0))::DECIMAL(15,2)
      ELSE 0::DECIMAL(15,2)
    END as average_order_value,
    COALESCE(SUM(
      COALESCE(nt.final_commission_amount, nt.commission_amount, 0)
    ), 0)::DECIMAL(15,2) as total_revenue
  FROM normalized_transactions nt
  WHERE DATE(nt.transaction_date) = target_date
    AND nt.platform IN ('strackr', 'shopmy')
  GROUP BY nt.platform
  ORDER BY nt.platform;
END;
$$;

-- Function to get comparison with previous day (day before the target date)
CREATE OR REPLACE FUNCTION get_metrics_comparison(
  current_date DATE DEFAULT CURRENT_DATE - INTERVAL '1 day',
  previous_date DATE DEFAULT CURRENT_DATE - INTERVAL '2 days'
)
RETURNS TABLE (
  platform TEXT,
  current_gmv DECIMAL(15,2),
  previous_gmv DECIMAL(15,2),
  gmv_change_percent DECIMAL(5,2),
  current_aov DECIMAL(15,2),
  previous_aov DECIMAL(15,2),
  aov_change_percent DECIMAL(5,2),
  current_revenue DECIMAL(15,2),
  previous_revenue DECIMAL(15,2),
  revenue_change_percent DECIMAL(5,2),
  current_transactions BIGINT,
  previous_transactions BIGINT
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  WITH current_metrics AS (
    SELECT * FROM get_previous_day_metrics(current_date)
  ),
  previous_metrics AS (
    SELECT * FROM get_previous_day_metrics(previous_date)
  )
  SELECT 
    COALESCE(c.platform, p.platform)::TEXT,
    COALESCE(c.total_gmv, 0)::DECIMAL(15,2) as current_gmv,
    COALESCE(p.total_gmv, 0)::DECIMAL(15,2) as previous_gmv,
    CASE 
      WHEN COALESCE(p.total_gmv, 0) > 0 THEN
        ((COALESCE(c.total_gmv, 0) - COALESCE(p.total_gmv, 0)) / COALESCE(p.total_gmv, 1) * 100)::DECIMAL(5,2)
      ELSE 0::DECIMAL(5,2)
    END as gmv_change_percent,
    COALESCE(c.average_order_value, 0)::DECIMAL(15,2) as current_aov,
    COALESCE(p.average_order_value, 0)::DECIMAL(15,2) as previous_aov,
    CASE 
      WHEN COALESCE(p.average_order_value, 0) > 0 THEN
        ((COALESCE(c.average_order_value, 0) - COALESCE(p.average_order_value, 0)) / COALESCE(p.average_order_value, 1) * 100)::DECIMAL(5,2)
      ELSE 0::DECIMAL(5,2)
    END as aov_change_percent,
    COALESCE(c.total_revenue, 0)::DECIMAL(15,2) as current_revenue,
    COALESCE(p.total_revenue, 0)::DECIMAL(15,2) as previous_revenue,
    CASE 
      WHEN COALESCE(p.total_revenue, 0) > 0 THEN
        ((COALESCE(c.total_revenue, 0) - COALESCE(p.total_revenue, 0)) / COALESCE(p.total_revenue, 1) * 100)::DECIMAL(5,2)
      ELSE 0::DECIMAL(5,2)
    END as revenue_change_percent,
    COALESCE(c.total_transactions, 0)::BIGINT as current_transactions,
    COALESCE(p.total_transactions, 0)::BIGINT as previous_transactions
  FROM current_metrics c
  FULL OUTER JOIN previous_metrics p ON c.platform = p.platform
  ORDER BY COALESCE(c.platform, p.platform);
END;
$$;

-- Create indexes for optimal performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_date_platform 
ON normalized_transactions(transaction_date, platform) 
WHERE platform IN ('strackr', 'shopmy');

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_date_amounts 
ON normalized_transactions(transaction_date, final_order_amount, order_amount, final_commission_amount, commission_amount) 
WHERE platform IN ('strackr', 'shopmy');