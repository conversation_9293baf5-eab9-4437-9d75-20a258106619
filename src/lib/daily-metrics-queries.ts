import { createClient } from '@supabase/supabase-js'

// Types for our data structures
export interface PlatformMetrics {
  platform: string
  total_transactions: number
  total_gmv: number
  average_order_value: number
  total_revenue: number
}

export interface MetricsComparison {
  platform: string
  current_gmv: number
  previous_gmv: number
  gmv_change_percent: number
  current_aov: number
  previous_aov: number
  aov_change_percent: number
  current_revenue: number
  previous_revenue: number
  revenue_change_percent: number
  current_transactions: number
  previous_transactions: number
}

export interface DailyReport {
  date: string
  strackr: PlatformMetrics | null
  shopmy: PlatformMetrics | null
  comparison: {
    strackr: MetricsComparison | null
    shopmy: MetricsComparison | null
  }
}

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

// Get yesterday's date in YYYY-MM-DD format
export function getYesterdayDate(): string {
  const yesterday = new Date()
  yesterday.setDate(yesterday.getDate() - 1)
  return yesterday.toISOString().split('T')[0]
}

// Get day before yesterday's date in YYYY-MM-DD format
export function getDayBeforeYesterdayDate(): string {
  const dayBefore = new Date()
  dayBefore.setDate(dayBefore.getDate() - 2)
  return dayBefore.toISOString().split('T')[0]
}

// Fetch raw transactions for a specific date and platform
async function fetchTransactionsForDate(date: string, platform?: string) {
  let query = supabase
    .from('normalized_transactions')
    .select(`
      platform,
      final_order_amount,
      order_amount,
      final_commission_amount,
      commission_amount,
      transaction_date
    `)
    .gte('transaction_date', `${date}T00:00:00.000Z`)
    .lt('transaction_date', `${date}T23:59:59.999Z`)

  if (platform) {
    query = query.eq('platform', platform)
  }

  const { data, error } = await query

  if (error) {
    console.error('Error fetching transactions:', error)
    throw new Error(`Failed to fetch transactions: ${error.message}`)
  }

  return data || []
}

// Calculate metrics from raw transaction data (reusing existing logic from analytics-utils.ts)
function calculateMetricsFromTransactions(transactions: any[], platform: string): PlatformMetrics {
  const platformTransactions = transactions.filter(t => t.platform === platform)
  
  if (platformTransactions.length === 0) {
    return {
      platform,
      total_transactions: 0,
      total_gmv: 0,
      average_order_value: 0,
      total_revenue: 0
    }
  }

  // Calculate GMV (using same logic as analytics-utils.ts)
  const totalGMV = platformTransactions.reduce((total, transaction) => {
    return total + (transaction.final_order_amount || transaction.order_amount || 0)
  }, 0)

  // Calculate Revenue
  const totalRevenue = platformTransactions.reduce((total, transaction) => {
    return total + (transaction.final_commission_amount || transaction.commission_amount || 0)
  }, 0)

  // Calculate AOV (only for transactions with order amount > 0)
  const validTransactions = platformTransactions.filter(transaction => {
    const orderAmount = transaction.final_order_amount || transaction.order_amount || 0
    return orderAmount > 0
  })

  const averageOrderValue = validTransactions.length > 0 ? totalGMV / validTransactions.length : 0

  return {
    platform,
    total_transactions: platformTransactions.length,
    total_gmv: totalGMV,
    average_order_value: averageOrderValue,
    total_revenue: totalRevenue
  }
}

// Fetch metrics for a specific date
export async function fetchDailyMetrics(date: string): Promise<PlatformMetrics[]> {
  try {
    // Fetch all transactions for the date
    const transactions = await fetchTransactionsForDate(date)
    
    // Calculate metrics for both platforms
    const strackrMetrics = calculateMetricsFromTransactions(transactions, 'strackr')
    const shopmyMetrics = calculateMetricsFromTransactions(transactions, 'shopmy')
    
    const results = []
    if (strackrMetrics.total_transactions > 0) results.push(strackrMetrics)
    if (shopmyMetrics.total_transactions > 0) results.push(shopmyMetrics)
    
    return results
  } catch (error) {
    console.error('Error in fetchDailyMetrics:', error)
    throw error
  }
}

// Calculate comparison between two sets of metrics
function calculateComparison(
  currentMetrics: PlatformMetrics | null,
  previousMetrics: PlatformMetrics | null,
  platform: string
): MetricsComparison | null {
  if (!currentMetrics && !previousMetrics) return null

  const current = currentMetrics || {
    platform,
    total_transactions: 0,
    total_gmv: 0,
    average_order_value: 0,
    total_revenue: 0
  }

  const previous = previousMetrics || {
    platform,
    total_transactions: 0,
    total_gmv: 0,
    average_order_value: 0,
    total_revenue: 0
  }

  // Calculate percentage changes
  const gmvChangePercent = previous.total_gmv > 0 
    ? ((current.total_gmv - previous.total_gmv) / previous.total_gmv) * 100
    : 0

  const aovChangePercent = previous.average_order_value > 0
    ? ((current.average_order_value - previous.average_order_value) / previous.average_order_value) * 100
    : 0

  const revenueChangePercent = previous.total_revenue > 0
    ? ((current.total_revenue - previous.total_revenue) / previous.total_revenue) * 100
    : 0

  return {
    platform,
    current_gmv: current.total_gmv,
    previous_gmv: previous.total_gmv,
    gmv_change_percent: gmvChangePercent,
    current_aov: current.average_order_value,
    previous_aov: previous.average_order_value,
    aov_change_percent: aovChangePercent,
    current_revenue: current.total_revenue,
    previous_revenue: previous.total_revenue,
    revenue_change_percent: revenueChangePercent,
    current_transactions: current.total_transactions,
    previous_transactions: previous.total_transactions
  }
}

// Fetch comparison between current and previous day
export async function fetchMetricsComparison(
  currentDate: string,
  previousDate: string
): Promise<{ strackr: MetricsComparison | null; shopmy: MetricsComparison | null }> {
  try {
    // Fetch metrics for both dates in parallel
    const [currentMetrics, previousMetrics] = await Promise.all([
      fetchDailyMetrics(currentDate),
      fetchDailyMetrics(previousDate)
    ])

    // Organize by platform
    const currentStrackr = currentMetrics.find(m => m.platform === 'strackr') || null
    const currentShopmy = currentMetrics.find(m => m.platform === 'shopmy') || null
    const previousStrackr = previousMetrics.find(m => m.platform === 'strackr') || null
    const previousShopmy = previousMetrics.find(m => m.platform === 'shopmy') || null

    // Calculate comparisons
    const strackrComparison = calculateComparison(currentStrackr, previousStrackr, 'strackr')
    const shopmyComparison = calculateComparison(currentShopmy, previousShopmy, 'shopmy')

    return {
      strackr: strackrComparison,
      shopmy: shopmyComparison
    }
  } catch (error) {
    console.error('Error in fetchMetricsComparison:', error)
    throw error
  }
}

// Main function to generate the complete daily report
export async function generateDailyReport(): Promise<DailyReport> {
  const yesterday = getYesterdayDate()
  const dayBeforeYesterday = getDayBeforeYesterdayDate()

  try {
    // Fetch current day metrics and comparison in parallel
    const [currentMetrics, comparison] = await Promise.all([
      fetchDailyMetrics(yesterday),
      fetchMetricsComparison(yesterday, dayBeforeYesterday)
    ])

    // Organize current metrics by platform
    const strackrMetrics = currentMetrics.find(m => m.platform === 'strackr') || null
    const shopmyMetrics = currentMetrics.find(m => m.platform === 'shopmy') || null

    return {
      date: yesterday,
      strackr: strackrMetrics,
      shopmy: shopmyMetrics,
      comparison
    }
  } catch (error) {
    console.error('Error generating daily report:', error)
    throw error
  }
}