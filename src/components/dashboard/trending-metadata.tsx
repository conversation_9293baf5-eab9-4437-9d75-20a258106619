"use client";

import React from "react";
import { Card, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { TrendingMetadata as TrendingMetadataType } from "@/hooks/useTrendingData";
import { CalendarIcon, SearchIcon, ShoppingBagIcon, TrendingUpIcon, UsersIcon, PackageIcon, ZapIcon } from "lucide-react";

interface TrendingMetadataProps {
  metadata?: TrendingMetadataType;
  loading?: boolean;
  error?: string;
}

export function TrendingMetadata({ metadata, loading, error }: TrendingMetadataProps) {
  if (loading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 7 }).map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-[100px]" />
              <Skeleton className="h-4 w-4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-[60px] mb-1" />
              <Skeleton className="h-3 w-[120px]" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-red-600">Error Loading Metadata</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">{error}</p>
        </CardContent>
      </Card>
    );
  }

  if (!metadata) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>No Data Available</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">No trending metadata available at this time.</p>
        </CardContent>
      </Card>
    );
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const metricCards = [
    {
      title: "Analysis Date",
      value: formatDate(metadata.analysis_date),
      description: `Generated ${formatDateTime(metadata.generated_at)}`,
      icon: CalendarIcon,
    },
    {
      title: "Lookback Period",
      value: `${metadata.lookback_days} days`,
      description: "Analysis timeframe",
      icon: ZapIcon,
    },
    {
      title: "Total Searches",
      value: metadata.total_searches.toLocaleString(),
      description: "Search queries analyzed",
      icon: SearchIcon,
    },
    {
      title: "Total Brands",
      value: metadata.total_brands.toLocaleString(),
      description: "Brands tracked",
      icon: UsersIcon,
    },
    {
      title: "Total Products",
      value: metadata.total_products.toLocaleString(),
      description: "Products analyzed",
      icon: PackageIcon,
    },
    {
      title: "Style Showdowns",
      value: metadata.total_showdowns.toLocaleString(),
      description: "Style comparisons",
      icon: TrendingUpIcon,
    },
  ];

  return (
    <div className="space-y-4">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">Analytics Overview</h2>
        <p className="text-muted-foreground">
          Key metrics from the latest trending analysis
        </p>
      </div>
      
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {metricCards.map((metric, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {metric.title}
              </CardTitle>
              <metric.icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metric.value}</div>
              <p className="text-xs text-muted-foreground">
                {metric.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
