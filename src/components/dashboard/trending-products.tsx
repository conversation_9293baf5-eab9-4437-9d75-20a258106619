"use client";

import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { TrendingProduct } from "@/hooks/useTrendingData";
import { PackageIcon, DollarSignIcon, TrendingUpIcon, SearchIcon, UsersIcon, ExternalLinkIcon, CalendarIcon } from "lucide-react";

interface TrendingProductsProps {
  trendingProducts?: TrendingProduct[];
  loading?: boolean;
  error?: string;
}

export function TrendingProducts({ trendingProducts, loading, error }: TrendingProductsProps) {
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-[200px]" />
          <Skeleton className="h-4 w-[300px]" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 10 }).map((_, i) => (
              <div key={i} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-5 w-[200px]" />
                  <Skeleton className="h-4 w-[150px]" />
                  <Skeleton className="h-4 w-[180px]" />
                </div>
                <div className="space-y-2">
                  <Skeleton className="h-6 w-[80px]" />
                  <Skeleton className="h-4 w-[60px]" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-red-600">Error Loading Trending Products</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">{error}</p>
        </CardContent>
      </Card>
    );
  }

  if (!trendingProducts || trendingProducts.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>No Trending Products Available</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">No trending product data available at this time.</p>
        </CardContent>
      </Card>
    );
  }

  const formatPrice = (price: number | null | undefined) => {
    if (price === null || price === undefined) {
      return 'N/A';
    }
    // Handle very high prices (likely data errors)
    if (price > 10000) {
      return `$${(price / 1000).toFixed(0)}k`;
    }
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  const formatTrendingScore = (score: number | null | undefined) => {
    if (score === null || score === undefined) {
      return {
        value: 'N/A',
        isPositive: true,
        color: 'text-gray-500',
        bgColor: 'bg-gray-50',
        label: 'Unknown',
      };
    }

    const isPositive = score >= 0;
    const isHighlyPositive = score > 100;
    const isHighlyNegative = score < -50;

    return {
      value: `${isPositive ? '+' : ''}${score.toFixed(1)}`,
      isPositive,
      color: isHighlyPositive ? 'text-emerald-600' :
             isPositive ? 'text-green-600' :
             isHighlyNegative ? 'text-red-700' : 'text-red-600',
      bgColor: isPositive ? 'bg-green-50' : 'bg-red-50',
      label: isHighlyPositive ? 'Hot' :
             isPositive ? 'Rising' :
             isHighlyNegative ? 'Declining' : 'Cooling',
    };
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
    });
  };

  // Show top 20 trending products by trending score
  const topProducts = trendingProducts
    .sort((a, b) => b.trending_score - a.trending_score)
    .slice(0, 20);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <PackageIcon className="h-5 w-5" />
          Trending Products
        </CardTitle>
        <CardDescription>
          Most trending products based on search volume, user engagement, and growth metrics
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="max-h-96 overflow-y-auto space-y-4">
          {topProducts.map((product, index) => {
            const trendingScore = formatTrendingScore(product.trending_score);

            return (
              <div key={index} className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                <div className="space-y-2 flex-1">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-muted-foreground">#{index + 1}</span>
                    <h3 className="font-semibold">{product.product_name}</h3>
                    <Badge variant="outline" className="text-xs">
                      {product.brand}
                    </Badge>
                  </div>

                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <SearchIcon className="h-3 w-3" />
                      {product.search_count.toLocaleString()} searches
                    </div>
                    <div className="flex items-center gap-1">
                      <UsersIcon className="h-3 w-3" />
                      {product.unique_users.toLocaleString()} users
                    </div>
                    <div className="flex items-center gap-1">
                      <CalendarIcon className="h-3 w-3" />
                      {product.days_searched} days
                    </div>
                    <div className="flex items-center gap-1">
                      <DollarSignIcon className="h-3 w-3" />
                      {formatPrice(product.avg_price)}
                    </div>
                  </div>

                  <div className="flex items-center gap-2 flex-wrap">
                    <Badge variant="secondary" className="text-xs">
                      {product.searches_per_day?.toFixed(1) || 'N/A'} searches/day
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      {product.ios_percentage?.toFixed(0) || 'N/A'}% iOS
                    </Badge>
                    {product.searches_per_user && (
                      <Badge variant="outline" className="text-xs">
                        {product.searches_per_user.toFixed(1)} searches/user
                      </Badge>
                    )}
                    <Badge variant="outline" className="text-xs">
                      {product.image_search_percentage || 'N/A'}% image search
                    </Badge>
                    {product.safari_percentage && product.safari_percentage > 0 && (
                      <Badge variant="outline" className="text-xs">
                        {product.safari_percentage.toFixed(1)}% Safari
                      </Badge>
                    )}
                    {product.sample_url && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 px-2 text-xs"
                        onClick={() => window.open(product.sample_url, '_blank')}
                      >
                        <ExternalLinkIcon className="h-3 w-3 mr-1" />
                        View
                      </Button>
                    )}
                  </div>
                </div>

                <div className="text-right space-y-2">
                  <div className={`text-lg font-bold ${trendingScore.color}`}>
                    {trendingScore.value}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Trending Score
                  </div>
                  <Badge
                    variant={trendingScore.isPositive ? "default" : "destructive"}
                    className="text-xs"
                  >
                    {trendingScore.label}
                  </Badge>
                  <div className="flex items-center gap-1 justify-end">
                    <TrendingUpIcon className="h-3 w-3 text-muted-foreground" />
                    <span className="text-xs text-muted-foreground">
                      {product.recent_searches} recent
                    </span>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {formatDate(product.first_searched)} - {formatDate(product.last_searched)}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}
