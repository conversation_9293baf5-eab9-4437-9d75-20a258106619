import { updateSession } from "./supabase/client/middleware";
import { type NextRequest, NextResponse } from "next/server";

export async function middleware(request: NextRequest) {
  try {
    const { response, supabase } = await updateSession(request, NextResponse.next());
    const nextUrl = request.nextUrl;

    const {
      data: { session },
    } = await supabase.auth.getSession();

    // Allow access to sign-in page, auth callback, and API routes without authentication
    const publicPaths = ["/sign-in", "/auth", "/api"];
    const isPublicPath = publicPaths.some(path => nextUrl.pathname.startsWith(path));

    // Check for local development bypass
    const isLocalDevelopment = process.env.NODE_ENV === "development";
    const allowSkipAuth = process.env.ALLOW_SKIP_AUTH === "true";
    const shouldSkipAuth = isLocalDevelopment && allowSkipAuth;

    // Not authenticated and trying to access protected route
    if (!session && !isPublicPath && !shouldSkipAuth) {
      const encodedSearchParams = `${nextUrl.pathname.substring(1)}${
        nextUrl.search
      }`;

      const url = new URL("/sign-in", request.url);

      if (encodedSearchParams) {
        url.searchParams.append("return_to", encodedSearchParams);
      }

      return NextResponse.redirect(url);
    }

    return response;
  } catch (error) {
    // In case of error, redirect to sign-in for safety
    return NextResponse.redirect(new URL("/sign-in", request.url));
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - Files with extensions (e.g., .png, .jpg, .css, .js)
     */
    "/((?!api|_next/static|_next/image|favicon.ico|.*\\..*).*)",
  ],
};
