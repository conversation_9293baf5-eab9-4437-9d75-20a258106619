"use client";

import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { BrandPerformanceData } from "@/hooks/useTrendingData";
import { TrendingUpIcon, TrendingDownIcon, UsersIcon, SearchIcon, ShoppingBagIcon, CalendarIcon } from "lucide-react";

interface BrandPerformanceProps {
  brandPerformance?: BrandPerformanceData[];
  loading?: boolean;
  error?: string;
}

export function BrandPerformance({ brandPerformance, loading, error }: BrandPerformanceProps) {
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-[200px]" />
          <Skeleton className="h-4 w-[300px]" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 10 }).map((_, i) => (
              <div key={i} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-5 w-[150px]" />
                  <Skeleton className="h-4 w-[200px]" />
                </div>
                <div className="space-y-2">
                  <Skeleton className="h-6 w-[80px]" />
                  <Skeleton className="h-4 w-[60px]" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-red-600">Error Loading Brand Performance</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">{error}</p>
        </CardContent>
      </Card>
    );
  }

  if (!brandPerformance || brandPerformance.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>No Brand Performance Data Available</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">No brand performance data available at this time.</p>
        </CardContent>
      </Card>
    );
  }

  const formatVelocity = (velocity: number) => {
    const isPositive = velocity >= 0;
    return {
      value: `${isPositive ? '+' : ''}${velocity.toFixed(1)}%`,
      isPositive,
      icon: isPositive ? TrendingUpIcon : TrendingDownIcon,
      color: isPositive ? 'text-green-600' : 'text-red-600',
    };
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
    });
  };

  // Show top 15 brands by total engagement
  const topBrands = brandPerformance
    .sort((a, b) => b.total_engagement - a.total_engagement)
    .slice(0, 15);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ShoppingBagIcon className="h-5 w-5" />
          Brand Performance
        </CardTitle>
        <CardDescription>
          Top performing brands by engagement, search volume, and trend velocity
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="max-h-96 overflow-y-auto space-y-4">
          {topBrands.map((brand, index) => {
            const velocity = formatVelocity(brand.trend_velocity);

            return (
              <div key={index} className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                <div className="space-y-2 flex-1">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-muted-foreground">#{index + 1}</span>
                    <h3 className="font-semibold capitalize">{brand.brand}</h3>
                  </div>

                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <UsersIcon className="h-3 w-3" />
                      {brand.unique_users.toLocaleString()} users
                    </div>
                    <div className="flex items-center gap-1">
                      <SearchIcon className="h-3 w-3" />
                      {brand.search_mentions.toLocaleString()} searches
                    </div>
                    <div className="flex items-center gap-1">
                      <ShoppingBagIcon className="h-3 w-3" />
                      {brand.product_searches.toLocaleString()} products
                    </div>
                    <div className="flex items-center gap-1">
                      <CalendarIcon className="h-3 w-3" />
                      {brand.days_active} days
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="text-xs">
                      {brand.engagement_per_day.toFixed(1)} eng/day
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      {brand.search_percentage.toFixed(1)}% search
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      {brand.product_search_percentage.toFixed(1)}% product
                    </Badge>
                  </div>
                </div>

                <div className="text-right space-y-2">
                  <div className="text-lg font-bold">
                    {brand.total_engagement.toLocaleString()}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Total Engagement
                  </div>
                  <div className={`flex items-center gap-1 justify-end ${velocity.color}`}>
                    <velocity.icon className="h-4 w-4" />
                    <span className="font-semibold text-sm">{velocity.value}</span>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {formatDate(brand.first_seen)} - {formatDate(brand.last_seen)}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}
