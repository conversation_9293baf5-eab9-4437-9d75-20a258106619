import { createClient } from "@/supabase/client/server";
import { redirect } from "next/navigation";
import TrendingPageClient from "./trending-client";

export default async function TrendingPage() {
  // Server-side authentication check
  const supabase = await createClient();
  const { data: { session } } = await supabase.auth.getSession();

  // Allow bypass in development mode
  const isLocalDevelopment = process.env.NODE_ENV === "development";
  const allowSkipAuth = process.env.ALLOW_SKIP_AUTH === "true";

  if (!session && !(isLocalDevelopment && allowSkipAuth)) {
    redirect('/sign-in');
  }

  // If authenticated, render the client component
  return <TrendingPageClient />;
}
